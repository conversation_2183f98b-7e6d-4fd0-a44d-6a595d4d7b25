# 2024电赛H题 - 循迹小车项目使用说明

## 项目概述

本项目是2024年全国大学生电子设计竞赛H题的循迹小车实现，基于STM32F407VGT6微控制器开发。小车具备自动循迹、角度控制、速度控制等功能，能够在指定赛道上自主行驶并完成相应任务。

## 主要功能

- **自动循迹**：基于灰度传感器的黑线循迹功能
- **角度控制**：利用MPU6050陀螺仪进行姿态控制
- **速度控制**：通过编码器反馈实现精确速度控制
- **多模式运行**：支持4种不同的运行模式
- **实时显示**：0.96寸OLED显示屏显示运行状态
- **串口调试**：支持串口通信进行参数调试

## 硬件配置

### 主控制器
- **MCU**: STM32F407VGT6
- **时钟频率**: 168MHz
- **Flash**: 1MB
- **RAM**: 192KB

### 传感器模块
- **MPU6050**: 六轴陀螺仪加速度计，用于姿态检测
- **灰度传感器阵列**: 8路灰度传感器，用于循迹检测
- **编码器**: 2个增量式编码器，用于速度和位置反馈

### 执行器
- **电机驱动**: TB6612FNG双路电机驱动模块
- **直流减速电机**: 2个带编码器的直流减速电机
- **电源**: 12V锂电池 + 降压模块

### 显示与交互
- **OLED显示屏**: 0.96寸128x64像素单色显示屏
- **按键**: 用户交互按键
- **LED指示灯**: 状态指示

### 通信接口
- **UART1**: 调试串口 (115200 baud)
- **UART2**: 扩展通信接口
- **I2C1**: MPU6050通信
- **I2C3**: OLED显示屏通信

## 软件架构

### 目录结构
```
2024_H_Car（New）/
├── Core/                    # STM32 HAL库核心文件
│   ├── Inc/                # 头文件
│   └── Src/                # 源文件
├── Drivers/                # STM32 HAL驱动库
├── User/                   # 用户代码
│   ├── App/               # 应用层代码
│   ├── Driver/            # 硬件驱动层
│   ├── Module/            # 功能模块
│   ├── MyDefine.h         # 全局头文件包含
│   ├── Scheduler.c/h      # 任务调度器
│   └── Scheduler_Task.c/h # 任务实现
├── MDK-ARM/               # Keil工程文件
└── 硬件方案.pdf           # 硬件设计方案
```

### 软件分层架构

#### 1. 应用层 (App)
- `encoder_app.c/h`: 编码器应用
- `motor_app.c/h`: 电机控制应用
- `mpu6050_app.c/h`: 陀螺仪应用
- `gray_app.c/h`: 灰度传感器应用
- `pid_app.c/h`: PID控制算法
- `oled_app.c/h`: OLED显示应用
- `uart_app.c/h`: 串口通信应用
- `key_app.c/h`: 按键处理应用
- `led_app.c/h`: LED控制应用

#### 2. 驱动层 (Driver)
- `encoder_driver.c/h`: 编码器底层驱动
- `motor_driver.c/h`: 电机底层驱动
- `mpu6050_driver.c/h`: MPU6050底层驱动
- `oled_driver.c/h`: OLED底层驱动
- `uart_driver.c/h`: 串口底层驱动
- `key_driver.c/h`: 按键底层驱动
- `led_driver.c/h`: LED底层驱动

#### 3. 模块层 (Module)
- `PID/`: PID控制算法模块
- `MPU6050/`: MPU6050传感器模块
- `0.96 Oled/`: OLED显示模块
- `Grayscale/`: 灰度传感器模块
- `Ebtn/`: 按键处理模块
- `Ringbuffer/`: 环形缓冲区模块
- `WouoUI-Page/`: UI界面模块

### 控制算法

#### PID控制系统
项目采用三环PID控制结构：

1. **速度环PID**: 控制左右电机的速度
   - 输入：编码器反馈的实际速度
   - 输出：电机PWM控制量

2. **循迹环PID**: 基于灰度传感器的循迹控制
   - 输入：循迹偏差
   - 输出：左右轮速度差值

3. **角度环PID**: 基于陀螺仪的角度控制
   - 输入：陀螺仪角度偏差
   - 输出：左右轮速度差值

#### 任务调度
采用时间片轮转的任务调度方式：
- **1ms**: 系统时基中断
- **5ms**: 传感器数据采集和PID控制
- **10ms**: 按键扫描
- **500ms**: LED状态指示

## 编译和烧录

### 开发环境要求
- **IDE**: Keil MDK-ARM 5.39或更高版本
- **编译器**: ARM Compiler 5.06
- **调试器**: ST-Link V2或J-Link
- **STM32CubeMX**: 用于硬件配置（可选）

### 编译步骤
1. 打开Keil MDK-ARM
2. 打开项目文件：`MDK-ARM/2024_H_Car.uvprojx`
3. 选择目标：`2024_H_Car`
4. 点击编译按钮或按F7进行编译
5. 确保编译无错误和警告

### 烧录步骤
1. 连接ST-Link调试器到STM32F407开发板
2. 在Keil中点击下载按钮或按F8
3. 等待烧录完成
4. 复位开发板开始运行

### 调试配置
- **调试接口**: SWD
- **时钟频率**: 1MHz
- **Flash下载算法**: STM32F4xx Flash
- **RAM**: 0x20000000, Size: 0x20000
- **Flash**: 0x08000000, Size: 0x100000

## 使用方法

### 系统启动
1. **硬件连接**：
   - 确保所有传感器和执行器正确连接
   - 检查电源连接（12V主电源）
   - 确认ST-Link调试器连接正常

2. **软件启动**：
   - 上电后系统自动初始化
   - OLED显示屏显示初始化信息
   - 串口输出："=== System Init ==="

### 运行模式

系统支持4种运行模式（通过`system_mode`变量控制）：

#### 模式1：基础循迹模式
- **功能**：基本的黑线循迹功能
- **控制方式**：循迹环PID控制
- **速度**：94 cm/s

#### 模式2：角度控制模式
- **功能**：基于陀螺仪的角度控制
- **控制方式**：角度环PID控制
- **速度**：130 cm/s（距离>20000时降至75 cm/s）

#### 模式3：混合控制模式
- **功能**：循迹和角度控制结合
- **适用场景**：复杂赛道

#### 模式4：自定义模式
- **功能**：用户自定义控制逻辑

### 操作流程

#### 启动流程
1. 上电系统初始化
2. 传感器校准（MPU6050零点校准）
3. 等待启动信号
4. 开始执行任务

#### 运行控制
- **启动/停止**：通过按键或串口命令控制
- **模式切换**：修改`system_mode`变量
- **参数调整**：通过串口发送调试命令

#### 状态监控
- **OLED显示**：实时显示传感器数据和运行状态
- **串口输出**：详细的调试信息
- **LED指示**：系统运行状态指示

### 参数配置

#### PID参数调整
```c
// 速度环PID参数
pid_params_left.kp = 80.0f;
pid_params_left.ki = 0.0f;
pid_params_left.kd = 0.0f;

// 循迹环PID参数
pid_params_line.kp = 1.0f;
pid_params_line.ki = 0.0f;
pid_params_line.kd = 0.0f;

// 角度环PID参数
pid_params_angle.kp = 1.0f;
pid_params_angle.ki = 0.0f;
pid_params_angle.kd = 0.0f;
```

#### 传感器配置
```c
// 编码器参数
#define ENCODER_PPR 1560        // 编码器每转脉冲数
#define WHEEL_CIRCUMFERENCE_CM 20.1f  // 车轮周长(cm)
#define SAMPLING_TIME_S 0.005f  // 采样时间(s)

// 灰度传感器阈值
#define GRAY_THRESHOLD 2000     // 黑白判断阈值
```

### 串口调试

#### 串口配置
- **波特率**：115200
- **数据位**：8
- **停止位**：1
- **校验位**：无
- **流控**：无

#### 调试命令
通过串口可以发送以下命令：
- 查看传感器数据
- 修改PID参数
- 切换运行模式
- 启动/停止系统

#### 输出信息
系统会通过串口输出：
- 初始化信息
- 传感器实时数据
- PID控制输出
- 错误和警告信息

## 硬件需求清单

### 必需硬件组件

#### 主控板
- **STM32F407VGT6开发板** × 1
  - 最小系统板或核心板
  - 包含晶振、复位电路、电源管理
  - 引出所需GPIO接口

#### 传感器模块
- **MPU6050陀螺仪模块** × 1
  - 6轴陀螺仪+加速度计
  - I2C接口
  - 3.3V供电

- **灰度传感器阵列** × 1
  - 8路红外灰度传感器
  - 数字输出接口
  - 可调节检测距离

- **增量式编码器** × 2
  - 配套减速电机使用
  - AB相正交输出
  - 分辨率：1560 PPR

#### 执行器
- **TB6612FNG电机驱动模块** × 1
  - 双路H桥驱动
  - 支持PWM调速
  - 最大电流1.2A

- **直流减速电机** × 2
  - 带编码器反馈
  - 12V供电
  - 减速比1:30

#### 显示模块
- **0.96寸OLED显示屏** × 1
  - 128×64像素
  - I2C接口
  - SSD1306控制器

#### 电源系统
- **12V锂电池** × 1
  - 容量≥2000mAh
  - 支持大电流放电

- **降压模块** × 1
  - 12V转5V/3.3V
  - 输出电流≥2A

#### 机械结构
- **小车底盘** × 1
  - 适合安装所有模块
  - 预留传感器安装位置

- **车轮** × 2
  - 直径约65mm
  - 橡胶材质，增加摩擦力

#### 连接线材
- **杜邦线**：公对公、公对母、母对母各若干
- **电源线**：适合电流的导线
- **USB数据线**：用于程序下载和调试

### 可选硬件组件
- **按键模块**：用于用户交互
- **LED指示灯**：状态显示
- **蜂鸣器**：声音提示
- **扩展传感器**：超声波、摄像头等

### 调试工具
- **ST-Link V2调试器** × 1
  - 用于程序下载和在线调试
  - 支持SWD接口

- **USB转TTL模块** × 1
  - 用于串口调试
  - 支持3.3V电平

## 故障排除

### 常见问题及解决方案

#### 1. 编译错误
**问题**：Keil编译时出现错误
**解决方案**：
- 检查Keil版本是否支持STM32F407
- 确认所有头文件路径正确配置
- 检查宏定义是否正确

#### 2. 下载失败
**问题**：程序无法下载到MCU
**解决方案**：
- 检查ST-Link连接是否正常
- 确认目标芯片型号选择正确
- 检查供电是否正常
- 尝试擦除芯片后重新下载

#### 3. 串口无输出
**问题**：串口调试无信息输出
**解决方案**：
- 检查串口线连接（TX、RX、GND）
- 确认波特率设置（115200）
- 检查串口驱动是否正常安装

#### 4. 传感器数据异常
**问题**：MPU6050或灰度传感器数据不正常
**解决方案**：
- 检查I2C连接线路
- 确认传感器供电电压
- 检查传感器初始化代码
- 使用示波器检查I2C时序

#### 5. 电机不转动
**问题**：电机驱动无响应
**解决方案**：
- 检查电机驱动模块连接
- 确认PWM信号输出正常
- 检查电机供电电压
- 测试电机驱动使能信号

#### 6. PID控制效果差
**问题**：循迹或速度控制不稳定
**解决方案**：
- 调整PID参数（Kp、Ki、Kd）
- 检查传感器数据质量
- 优化控制周期
- 分析系统响应特性

### 调试技巧

#### 1. 分步调试
- 先测试单个模块功能
- 逐步集成各个子系统
- 使用串口输出调试信息

#### 2. 硬件检查
- 使用万用表检查连接
- 示波器观察信号波形
- 逻辑分析仪分析数字信号

#### 3. 软件调试
- 使用Keil在线调试功能
- 设置断点观察变量值
- 单步执行分析程序流程

## 技术支持

### 文档资源
- STM32F407数据手册
- HAL库用户手册
- 各传感器模块说明书
- 硬件方案.pdf

### 在线资源
- STM32官方网站
- Keil官方文档
- 相关技术论坛

### 联系方式
如有技术问题，请通过以下方式联系：
- 项目仓库Issues
- 技术交流群
- 邮件支持

---

**注意事项**：
1. 使用前请仔细阅读硬件连接图
2. 确保电源极性正确，避免烧毁器件
3. 调试时注意安全，避免短路
4. 定期备份重要代码和配置
