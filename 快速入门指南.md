# 2024电赛H题循迹小车 - 快速入门指南

## 🚀 5分钟快速上手

### 第一步：准备工作
1. **硬件检查**
   - ✅ STM32F407开发板
   - ✅ ST-Link调试器
   - ✅ USB数据线
   - ✅ 12V电源

2. **软件准备**
   - ✅ 安装Keil MDK-ARM
   - ✅ 下载项目代码
   - ✅ 安装ST-Link驱动

### 第二步：编译下载
1. **打开项目**
   ```
   双击打开：MDK-ARM/2024_H_Car.uvprojx
   ```

2. **编译程序**
   ```
   按F7或点击编译按钮
   确保无错误和警告
   ```

3. **下载程序**
   ```
   连接ST-Link到开发板
   按F8或点击下载按钮
   ```

### 第三步：基础测试
1. **上电测试**
   - 连接12V电源
   - 观察LED指示灯
   - 检查OLED显示

2. **串口测试**
   - 连接USB转TTL模块
   - 打开串口调试助手
   - 波特率设置为115200
   - 观察输出信息

## 🔧 硬件连接图

### 主要连接
```
STM32F407 ←→ 外设连接
├── I2C1 ←→ MPU6050 (陀螺仪)
├── I2C3 ←→ OLED显示屏
├── TIM1 ←→ 电机PWM输出
├── TIM3 ←→ 左编码器
├── TIM4 ←→ 右编码器
├── UART1 ←→ 调试串口
└── GPIO ←→ 灰度传感器阵列
```

### 引脚分配
| 功能 | 引脚 | 说明 |
|------|------|------|
| MPU6050_SCL | PB6 | I2C1时钟线 |
| MPU6050_SDA | PB7 | I2C1数据线 |
| OLED_SCL | PA8 | I2C3时钟线 |
| OLED_SDA | PC9 | I2C3数据线 |
| 电机PWM | PA8/PA9 | TIM1_CH1/CH2 |
| 编码器A | PA6/PB6 | TIM3/TIM4 |
| 编码器B | PA7/PB7 | TIM3/TIM4 |
| 调试串口 | PA9/PA10 | UART1_TX/RX |

## ⚙️ 基础配置

### 系统参数
```c
// 基础速度设置
int basic_speed = 130;  // 基础速度 (cm/s)

// 系统模式
unsigned char system_mode = 4;  // 1-4对应不同模式

// PID使能
bool pid_running = false;  // PID控制开关
```

### PID参数
```c
// 速度环PID (推荐起始值)
pid_params_left.kp = 80.0f;
pid_params_left.ki = 0.0f;
pid_params_left.kd = 0.0f;

// 循迹环PID (推荐起始值)
pid_params_line.kp = 1.0f;
pid_params_line.ki = 0.0f;
pid_params_line.kd = 0.0f;
```

## 🎮 操作说明

### 启动流程
1. **系统初始化**
   ```
   上电 → 硬件初始化 → 传感器校准 → 等待启动
   ```

2. **开始运行**
   ```c
   // 通过代码启动
   pid_running = true;
   
   // 或通过按键启动
   // 按键处理在Key_Task()中实现
   ```

### 模式切换
```c
// 修改运行模式
system_mode = 1;  // 基础循迹
system_mode = 2;  // 角度控制
system_mode = 3;  // 混合模式
system_mode = 4;  // 自定义模式
```

### 参数调整
```c
// 运行时调整速度
basic_speed = 100;  // 降低速度

// 调整PID参数
pid_params_line.kp = 1.5f;  // 增加比例系数
```

## 📊 调试方法

### 串口调试
1. **查看传感器数据**
   ```
   串口输出包含：
   - 编码器速度
   - 陀螺仪角度
   - 灰度传感器状态
   - PID输出值
   ```

2. **调试命令**
   ```c
   // 在代码中添加调试输出
   Uart_Printf(&huart1, "Speed: L=%.2f R=%.2f\r\n", 
               left_encoder.speed_cm_s, 
               right_encoder.speed_cm_s);
   ```

### OLED显示
```c
// 显示关键信息
Oled_Printf(0, 0, "Mode:%d", system_mode);
Oled_Printf(0, 1, "Speed:%d", basic_speed);
Oled_Printf(0, 2, "Angle:%.1f", Yaw);
```

### LED指示
```c
// LED状态指示
LED1 = 系统运行状态
LED2 = PID控制状态
LED3 = 错误指示
LED4 = 用户自定义
```

## 🔍 常见问题

### Q1: 编译错误
**A**: 检查Keil版本和路径配置
```
Project → Options → C/C++ → Include Paths
确保所有路径正确添加
```

### Q2: 下载失败
**A**: 检查ST-Link连接
```
1. 确认ST-Link驱动安装
2. 检查连接线路
3. 尝试擦除芯片后重新下载
```

### Q3: 串口无输出
**A**: 检查串口配置
```
1. 确认波特率115200
2. 检查TX/RX连接
3. 确认串口驱动正常
```

### Q4: 传感器无数据
**A**: 检查I2C连接
```
1. 确认SCL/SDA连接正确
2. 检查上拉电阻
3. 验证传感器供电
```

### Q5: 电机不转
**A**: 检查电机驱动
```
1. 确认PWM输出正常
2. 检查电机驱动使能
3. 验证电机供电电压
```

## 📈 性能优化

### 调试技巧
1. **分步测试**
   - 先测试单个模块
   - 逐步集成功能
   - 最后整体调试

2. **参数调优**
   - 从小参数开始
   - 逐步增加增益
   - 观察系统响应

3. **数据记录**
   - 记录调试过程
   - 保存有效参数
   - 建立参数库

### 进阶功能
1. **自适应控制**
   - 根据环境自动调整参数
   - 实现智能模式切换

2. **数据记录**
   - 记录运行轨迹
   - 分析性能数据

3. **远程控制**
   - 添加无线通信模块
   - 实现远程监控

## 📚 学习资源

### 官方文档
- [STM32F407参考手册](https://www.st.com/resource/en/reference_manual/dm00031020.pdf)
- [HAL库用户手册](https://www.st.com/resource/en/user_manual/dm00105879.pdf)

### 在线教程
- STM32中文社区
- 正点原子教程
- 野火电子教程

### 技术论坛
- STM32官方论坛
- 电子发烧友论坛
- CSDN技术博客

---

**🎯 下一步计划**
1. 完成基础功能测试
2. 优化PID控制参数
3. 添加高级功能
4. 准备比赛调试

**💡 提示**
- 保持代码整洁和注释完整
- 定期备份重要配置
- 记录调试过程和参数
- 与团队成员分享经验
